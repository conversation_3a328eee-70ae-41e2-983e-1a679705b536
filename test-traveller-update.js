const fetch = require('node-fetch');

async function testTravellerUpdate() {
  const baseUrl = 'http://localhost:9000';
  const customerId = 'cus_01K0QZD63W6BXC6HBT2M8ZA8K3';
  const travellerId = 'cust_trav_01K0R05SHRBVSZJYA1PJ41M1HS';
  
  // Test data for updating traveller
  const updateData = {
    first_name: 'Updated',
    last_name: 'Traveller',
    gender: 'male',
    date_of_birth: '1990-01-01',
    relationship: 'spouse'
  };

  try {
    console.log('Testing traveller update...');
    console.log('URL:', `${baseUrl}/admin/all-customers/${customerId}/travellers/${travellerId}`);
    console.log('Data:', JSON.stringify(updateData, null, 2));

    const response = await fetch(`${baseUrl}/admin/all-customers/${customerId}/travellers/${travellerId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        // Add any required auth headers here if needed
      },
      body: JSON.stringify(updateData)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseData = await response.text();
    console.log('Response body:', responseData);

    if (response.ok) {
      console.log('✅ Update successful!');
      try {
        const jsonData = JSON.parse(responseData);
        console.log('Updated traveller:', JSON.stringify(jsonData, null, 2));
      } catch (e) {
        console.log('Response is not JSON');
      }
    } else {
      console.log('❌ Update failed');
    }

  } catch (error) {
    console.error('Error testing traveller update:', error);
  }
}

// Also test GET to see if the traveller exists
async function testTravellerGet() {
  const baseUrl = 'http://localhost:9000';
  const customerId = 'cus_01K0QZD63W6BXC6HBT2M8ZA8K3';
  const travellerId = 'cust_trav_01K0R05SHRBVSZJYA1PJ41M1HS';

  try {
    console.log('\nTesting traveller GET...');
    console.log('URL:', `${baseUrl}/admin/all-customers/${customerId}/travellers/${travellerId}`);

    const response = await fetch(`${baseUrl}/admin/all-customers/${customerId}/travellers/${travellerId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    console.log('Response status:', response.status);
    const responseData = await response.text();
    console.log('Response body:', responseData);

    if (response.ok) {
      console.log('✅ GET successful!');
    } else {
      console.log('❌ GET failed');
    }

  } catch (error) {
    console.error('Error testing traveller GET:', error);
  }
}

// Run both tests
async function runTests() {
  await testTravellerGet();
  await testTravellerUpdate();
}

runTests();
